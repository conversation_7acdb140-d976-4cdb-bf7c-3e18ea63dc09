/**
* Template Name: Scout
* Template URL: https://bootstrapmade.com/scout-bootstrap-multipurpose-template/
* Updated: May 05 2025 with Bootstrap v5.3.5
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# Font & Color Variables
# Help: https://bootstrapmade.com/color-system/
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Roboto",  system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Raleway",  sans-serif;
  --nav-font: "Montserrat",  sans-serif;
}

/* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
:root { 
  --background-color: #ffffff; /* Background color for the entire website, including individual sections */
  --default-color: #52413a; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #31221c; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #f85d23; /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #ffffff; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: #52413a;  /* The default color of the main navmenu links */
  --nav-hover-color: #f85d23; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #ffffff; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #ffffff; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #52413a; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #f85d23; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #f8f5f4;
  --surface-color: #ffffff;
}

.dark-background {
  --background-color: #060606;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #343333;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 15px 0;
  transition: all 0.5s;
  z-index: 997;
}

.header .logo {
  line-height: 1;
}

.header .logo img {
  max-height: 36px;
  margin-right: 8px;
}

.header .logo h1 {
  font-size: 26px;
  margin: 0;
  font-weight: 600;
  color: var(--heading-color);
}

.btn-getstarted,
.btn-getstarted:focus {
  color: var(--contrast-color);
  background: var(--accent-color);
  font-size: 14px;
  padding: 8px 25px;
  border-radius: 50px;
  transition: 0.3s;
  display: inline-block;
  text-align: center;
  line-height: 1.5; /* Added for better vertical alignment */
  border: none; /* Removed border */
}

.btn-getstarted:hover,
.btn-getstarted:focus:hover {
  color: var(--contrast-color);
  background: color-mix(in srgb, var(--accent-color), transparent 15%);
}

/* Specific margin for the button in the header */
.header .btn-getstarted,
.header .btn-getstarted:focus {
  margin: 0 0 0 30px;
}

@media (max-width: 1200px) {
  .header .logo {
    order: 1;
  }

  .header .btn-getstarted {
    order: 2;
    margin: 0 15px 0 0;
    padding: 6px 15px;
  }

  .header .navmenu {
    order: 3;
  }
}

/* Global Header on Scroll
------------------------------*/
.scrolled .header {
  --background-color: rgba(244, 239, 237, 0.95);
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Desktop Navigation */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu>ul>li {
    white-space: nowrap;
    padding: 15px 14px;
  }

  .navmenu>ul>li:last-child {
    padding-right: 0;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    font-size: 15px;
    padding: 0 2px;
    font-family: var(--nav-font);
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
    position: relative;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu>ul>li>a:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -6px;
    left: 0;
    background-color: var(--accent-color);
    visibility: hidden;
    width: 0px;
    transition: all 0.3s ease-in-out 0s;
  }

  .navmenu a:hover:before,
  .navmenu li:hover>a:before,
  .navmenu .active:before {
    visibility: visible;
    width: 100%;
  }

  .navmenu li:hover>a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover>a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover>ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover>ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Mobile Navigation */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 28px;
    line-height: 0;
    margin-right: 10px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .navmenu {
    padding: 0;
    z-index: 9997;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 60px 20px 20px 20px;
    padding: 10px 0;
    margin: 0;
    border-radius: 6px;
    background-color: var(--nav-mobile-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    overflow-y: auto;
    transition: 0.3s;
    z-index: 9998;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-dropdown-color);
    padding: 10px 20px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    transition: 0.3s;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: 0.3s;
    background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown>.dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: absolute;
    font-size: 32px;
    top: 15px;
    right: 15px;
    margin-right: 0;
    z-index: 9999;
  }

  .mobile-nav-active .navmenu {
    position: fixed;
    overflow: hidden;
    inset: 0;
    background: rgba(33, 37, 41, 0.8);
    transition: 0.3s;
  }

  .mobile-nav-active .navmenu>ul {
    display: block;
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  position: relative;
}

.footer .footer-top {
  padding-top: 50px;
}

.footer .footer-about .logo {
  line-height: 1;
  margin-bottom: 25px;
}

.footer .footer-about .logo img {
  max-height: 40px;
  margin-right: 6px;
}

.footer .footer-about .logo span {
  color: var(--heading-color);
  font-family: var(--heading-font);
  font-size: 26px;
  font-weight: 700;
  letter-spacing: 1px;
}

.footer .footer-about p {
  font-size: 14px;
  font-family: var(--heading-font);
}

.footer .social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-right: 10px;
  transition: 0.3s;
}

.footer .social-links a:hover {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.footer h4 {
  font-size: 16px;
  font-weight: bold;
  position: relative;
  padding-bottom: 12px;
}

.footer .footer-links {
  margin-bottom: 30px;
}

.footer .footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer .footer-links ul i {
  padding-right: 2px;
  font-size: 12px;
  line-height: 0;
}

.footer .footer-links ul li {
  padding: 10px 0;
  display: flex;
  align-items: center;
}

.footer .footer-links ul li:first-child {
  padding-top: 0;
}

.footer .footer-links ul a {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  display: inline-block;
  line-height: 1;
}

.footer .footer-links ul a:hover {
  color: var(--accent-color);
}

.footer .footer-contact p {
  margin-bottom: 5px;
}

.footer .copyright {
  padding: 25px 0;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.footer .copyright p {
  margin-bottom: 0;
}

.footer .credits {
  margin-top: 8px;
  font-size: 13px;
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 25px 0;
  position: relative;
}

.page-title h1 {
  font-size: 24px;
  font-weight: 700;
}

.page-title .breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
}

.page-title .breadcrumbs ol li+li {
  padding-left: 10px;
}

.page-title .breadcrumbs ol li+li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  scroll-margin-top: 82px;
  overflow: clip;
}

@media (max-width: 1199px) {

  section,
  .section {
    scroll-margin-top: 63px;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  position: relative;
}

.section-title h2:after {
  content: "";
  position: absolute;
  display: block;
  width: 50px;
  height: 3px;
  background: var(--accent-color);
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.section-title p {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  padding: 80px 0;
}

.hero .hero-content {
  margin-bottom: 2rem;
}

@media (min-width: 992px) {
  .hero .hero-content {
    margin-bottom: 0;
  }
}

.hero .hero-tag {
  display: inline-flex;
  align-items: center;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  margin-bottom: 1rem;
}

.hero .hero-tag i {
  color: var(--accent-color);
  margin-right: 0.5rem;
}

.hero .hero-tag span {
  color: var(--accent-color);
  font-weight: 600;
}

.hero h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.hero h1 .highlight {
  color: var(--accent-color);
}

@media (min-width: 992px) {
  .hero h1 {
    font-size: 3.5rem;
  }
}

.hero .lead {
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  margin-bottom: 1.5rem;
}

.hero .hero-features {
  list-style: none;
  padding-left: 0;
  margin-bottom: 2rem;
}

.hero .hero-features li {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.hero .hero-features li i {
  color: var(--accent-color);
  margin-right: 0.5rem;
}

.hero .hero-cta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.hero .hero-cta .btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--contrast-color);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.hero .hero-cta .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), black 10%);
  border-color: color-mix(in srgb, var(--accent-color), black 10%);
}

.hero .hero-cta .btn-link {
  color: var(--heading-color);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.hero .hero-cta .btn-link i {
  margin-right: 0.5rem;
}

.hero .hero-cta .btn-link:hover {
  color: var(--accent-color);
}

.hero .hero-image-wrapper {
  position: relative;
  padding: 0 40px;
}

.hero .hero-image-wrapper .hero-image {
  border-radius: 10px;
}

.hero .hero-image-wrapper .stat-card {
  position: absolute;
  background-color: var(--surface-color);
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 150px;
}

.hero .hero-image-wrapper .stat-card.top-right {
  top: 40px;
  right: 0px;
}

.hero .hero-image-wrapper .stat-card.bottom-left {
  bottom: 40px;
  left: 0px;
}

.hero .hero-image-wrapper .stat-card .stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
  margin-bottom: 0.25rem;
}

.hero .hero-image-wrapper .stat-card .stat-label {
  font-size: 0.875rem;
  color: var(--heading-color);
  margin-bottom: 0.5rem;
}

.hero .hero-image-wrapper .stat-card .stat-icon {
  align-self: flex-end;
  font-size: 1.25rem;
  color: color-mix(in srgb, var(--accent-color), transparent 50%);
}

@media (max-width: 575px) {
  .hero .hero-image-wrapper {
    padding: 0;
  }

  .hero .hero-image-wrapper .stat-card {
    position: relative;
  }

  .hero .hero-image-wrapper .stat-card.top-right,
  .hero .hero-image-wrapper .stat-card.bottom-left {
    inset: auto;
    margin-top: 30px;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .section-heading {
  font-size: 2.5rem;
  color: var(--heading-color);
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 1rem;
  font-weight: 700;
}

.about .section-heading:after {
  content: "";
  position: absolute;
  width: 70px;
  height: 3px;
  background: var(--accent-color);
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.about .lead {
  font-size: 1.2rem;
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.about .feature-box {
  background: var(--surface-color);
  border-radius: 12px;
  padding: 2.5rem 1.5rem;
  text-align: center;
  height: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.about .feature-box:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background: color-mix(in srgb, var(--accent-color), transparent 92%);
  z-index: -1;
  transition: height 0.5s cubic-bezier(0.65, 0, 0.35, 1);
  border-radius: 12px;
}

.about .feature-box:hover {
  transform: translateY(-10px);
  box-shadow: none;
}

.about .feature-box:hover:before {
  height: 100%;
}

.about .feature-box:hover .icon-container {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.about .feature-box .icon-container {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 1.8rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px color-mix(in srgb, var(--accent-color), transparent 85%);
}

.about .feature-box h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--heading-color);
}

.about .feature-box p {
  color: color-mix(in srgb, var(--default-color), transparent 25%);
  font-size: 0.95rem;
  line-height: 1.6;
}

.about .about-content-box {
  padding: 2rem;
}

.about .about-content-box h3 {
  font-size: 2rem;
  color: var(--heading-color);
  margin-bottom: 1.5rem;
  font-weight: 700;
  line-height: 1.3;
}

.about .about-content-box p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.about .about-content-box .progress-item {
  margin-bottom: 1.2rem;
}

.about .about-content-box .progress-item .progress-title {
  font-weight: 600;
  color: var(--heading-color);
  font-size: 1rem;
}

.about .about-content-box .progress-item .progress-percent {
  font-weight: 700;
  color: var(--accent-color);
}

.about .about-content-box .progress-item .progress {
  height: 8px;
  border-radius: 4px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  margin-top: 0.5rem;
  overflow: hidden;
}

.about .about-content-box .progress-item .progress .progress-bar {
  background-color: var(--accent-color);
  border-radius: 4px;
}

.about .about-content-box .btn-discover {
  background: var(--accent-color);
  color: var(--contrast-color);
  border: none;
  border-radius: 30px;
  padding: 0.8rem 2rem;
  font-weight: 600;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.about .about-content-box .btn-discover:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: color-mix(in srgb, var(--accent-color), #000 15%);
  transition: width 0.3s ease;
  z-index: -1;
  border-radius: 30px;
}

.about .about-content-box .btn-discover:hover {
  box-shadow: 0 8px 25px color-mix(in srgb, var(--accent-color), transparent 80%);
}

.about .about-content-box .btn-discover:hover:before {
  width: 100%;
}

.about .about-image-grid {
  position: relative;
  height: 540px;
  margin: 0 2rem;
}

.about .about-image-grid .img-grid-main {
  position: absolute;
  top: 0;
  right: 0;
  width: 70%;
  height: 350px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.about .about-image-grid .img-grid-secondary {
  position: absolute;
  bottom: 0;
  right: 30%;
  width: 50%;
  height: 220px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.about .about-image-grid .img-grid-tertiary {
  position: absolute;
  left: 0;
  bottom: 70px;
  width: 40%;
  height: 260px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.about .about-image-grid .experience-badge {
  position: absolute;
  right: 10px;
  bottom: 30px;
  width: 120px;
  height: 120px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--contrast-color);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 4;
}

.about .about-image-grid .experience-badge .years {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0.2rem;
}

.about .about-image-grid .experience-badge .text {
  font-size: 0.8rem;
  text-align: center;
  max-width: 90px;
  line-height: 1.2;
}

@media (max-width: 1199px) {
  .about .about-image-grid {
    height: 480px;
  }

  .about .about-image-grid .img-grid-main {
    height: 300px;
  }

  .about .about-image-grid .img-grid-secondary {
    height: 200px;
  }

  .about .about-image-grid .img-grid-tertiary {
    height: 220px;
  }
}

@media (max-width: 991px) {
  .about .section-heading {
    font-size: 2.2rem;
  }

  .about .about-content-box {
    padding: 1.5rem;
    margin-top: 2rem;
  }

  .about .about-content-box h3 {
    font-size: 1.8rem;
  }

  .about .about-image-grid {
    height: 450px;
    margin: 0 auto;
    max-width: 500px;
  }

  .about .about-image-grid .experience-badge {
    width: 100px;
    height: 100px;
  }

  .about .about-image-grid .experience-badge .years {
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .about .section-heading {
    font-size: 1.8rem;
  }

  .about .feature-box {
    padding: 1.8rem 1rem;
  }

  .about .feature-box .icon-container {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .about .about-image-grid {
    height: 400px;
    margin-bottom: 2rem;
  }

  .about .about-image-grid .img-grid-main {
    width: 75%;
    height: 250px;
  }

  .about .about-image-grid .img-grid-secondary {
    width: 55%;
    height: 180px;
  }

  .about .about-image-grid .img-grid-tertiary {
    width: 40%;
    height: 180px;
  }
}

/*--------------------------------------------------------------
# Stats Section
--------------------------------------------------------------*/
.stats .stats-overview {
  height: 100%;
  padding: 30px;
}

.stats .stats-overview h3 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 15px;
}

.stats .stats-overview h3::after {
  content: "";
  position: absolute;
  display: block;
  width: 60px;
  height: 4px;
  background: var(--accent-color);
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
}

@media (min-width: 992px) {
  .stats .stats-overview h3::after {
    margin: 0;
  }
}

.stats .stats-overview p {
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  line-height: 1.7;
  margin-bottom: 0;
}

.stats .stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.stats .stats-card {
  background: var(--surface-color);
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats .stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats .stats-card .stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 50%;
  margin-right: 15px;
  flex-shrink: 0;
}

.stats .stats-card .stats-icon i {
  font-size: 24px;
  color: var(--accent-color);
}

.stats .stats-card .stats-content {
  flex-grow: 1;
}

.stats .stats-card .stats-content .stats-number {
  display: flex;
  align-items: baseline;
}

.stats .stats-card .stats-content .stats-number .purecounter {
  font-size: 32px;
  font-weight: 700;
  color: var(--heading-color);
  margin: 0;
  line-height: 1;
}

.stats .stats-card .stats-content .stats-number .plus {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-color);
  margin-left: 2px;
}

.stats .stats-card .stats-content p {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin: 5px 0 0;
  font-weight: 500;
  font-family: var(--heading-font);
}

@media (max-width: 768px) {
  .stats .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stats .stats-card {
    padding: 15px;
  }

  .stats .stats-card .stats-icon {
    width: 50px;
    height: 50px;
  }

  .stats .stats-card .stats-icon i {
    font-size: 20px;
  }

  .stats .stats-card .stats-content .stats-number .purecounter {
    font-size: 28px;
  }

  .stats .stats-card .stats-content .stats-number .plus {
    font-size: 20px;
  }
}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services {
  padding-top: 60px;
  padding-bottom: 30px;
}

.services .services-row {
  position: relative;
}

.services .services-headline .services-subtitle {
  color: var(--accent-color);
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 18px;
}

.services .services-headline .services-title {
  color: var(--heading-color);
  font-size: 2.5rem;
  line-height: 1.2;
  font-weight: 700;
  margin-bottom: 20px;
}

.services .services-description {
  margin-bottom: 30px;
}

.services .services-description p {
  font-size: 1.1rem;
  line-height: 1.6;
}

.services .services-image-container {
  position: relative;
  margin-bottom: 30px;
}

.services .services-image-container .services-image {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  height: 460px;
}

.services .services-image-container .services-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.services .services-grid {
  margin-left: 50px;
}

@media (max-width: 991px) {
  .services .services-grid {
    margin-left: 0;
    margin-top: 70px;
  }
}

.services .service-card {
  background-color: color-mix(in srgb, var(--accent-color), transparent 92%);
  border-radius: 25px;
  padding: 30px 25px;
  margin-bottom: 25px;
  border: 1px solid rgba(154, 154, 154, 0.3);
  transition: all 0.3s ease;
  height: 100%;
}

.services .service-card:hover {
  background-color: var(--accent-color);
  transform: translateY(-5px);
}

.services .service-card:hover .service-icon {
  color: var(--contrast-color);
  transform: scale(0.95);
}

.services .service-card:hover .service-info h3 a,
.services .service-card:hover .service-info p {
  color: var(--contrast-color);
  transform: scale(0.95);
}

.services .service-card:hover .service-content .read-more-btn {
  background-color: color-mix(in srgb, var(--contrast-color), transparent 84%);
  color: var(--contrast-color);
}

.services .service-content .service-icon {
  width: 65px;
  height: 65px;
  line-height: 65px;
  margin: 0 auto;
  font-size: 48px;
  color: var(--heading-color);
  transition: all 0.3s ease;
}

.services .service-content .service-info h3 {
  font-size: 1.4rem;
  margin: 15px 0;
  transition: all 0.3s ease;
}

.services .service-content .service-info h3 a {
  color: var(--heading-color);
  text-decoration: none;
}

.services .service-content .service-info p {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.services .service-content .service-action {
  margin-top: 20px;
}

.services .service-content .service-action .read-more-btn {
  display: inline-block;
  padding: 8px 16px;
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border-radius: 5px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.services .service-content .service-action .read-more-btn:hover {
  transform: translateX(5px);
}

.services .service-content .service-action .read-more-btn i {
  margin-left: 5px;
}

@media (max-width: 767px) {
  .services .services-headline .services-title {
    font-size: 2rem;
  }

  .services .services-image-container .services-image {
    height: 350px;
  }

  .services .services-image-container .circular-btn {
    bottom: -40px;
    right: 50%;
    transform: translateX(50%);
  }

  .services .services-image-container .circular-btn .circular-link {
    width: 130px;
    height: 130px;
    font-size: 0.9rem;
  }
}

/*--------------------------------------------------------------
# Clients Section
--------------------------------------------------------------*/
.clients {
  overflow-x: hidden;
  padding: 40px 0;
  /* Responsive adjustments */
}

.clients .clients-slider {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 20px 0;
}

.clients .clients-slider:not(:last-child) {
  margin-bottom: 20px;
}

.clients .clients-track {
  display: flex;
  width: fit-content;
  animation-duration: 30s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.clients .clients-track.track-1 {
  animation-name: scroll-left;
}

.clients .clients-track.track-2 {
  animation-name: scroll-right;
}

.clients .clients-track:hover {
  animation-play-state: paused;
}

.clients .clients-slide {
  flex: 0 0 auto;
  width: 200px;
  height: 100px;
  margin: 0 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-color);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.clients .clients-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, color-mix(in srgb, var(--default-color), transparent 96%), transparent);
  transition: 0.5s;
}

.clients .clients-slide:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.clients .clients-slide:hover::before {
  left: 100%;
}

.clients .clients-slide:hover img {
  filter: none;
  opacity: 1;
}

.clients .clients-slide img {
  max-width: 80%;
  max-height: 60%;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-2080px);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-2080px);
  }

  100% {
    transform: translateX(0);
  }
}

@media (max-width: 991px) {
  .clients .clients-slide {
    width: 180px;
    height: 90px;
    margin: 0 20px;
  }

  @keyframes scroll-left {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-1760px);
    }
  }

  @keyframes scroll-right {
    0% {
      transform: translateX(-1760px);
    }

    100% {
      transform: translateX(0);
    }
  }
}

@media (max-width: 767px) {
  .clients .clients-slide {
    width: 150px;
    height: 75px;
    margin: 0 15px;
  }

  @keyframes scroll-left {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-1440px);
    }
  }

  @keyframes scroll-right {
    0% {
      transform: translateX(-1440px);
    }

    100% {
      transform: translateX(0);
    }
  }
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials .testimonial-item {
  background-color: var(--surface-color);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonials .testimonial-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.testimonials .testimonial-item .stars {
  margin-bottom: 15px;
  color: #FFD700;
}

.testimonials .testimonial-item .stars i {
  margin-right: 2px;
}

.testimonials .testimonial-item p {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 25px;
  color: var(--default-color);
}

.testimonials .testimonial-item .testimonial-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.testimonials .testimonial-item .testimonial-footer .testimonial-author {
  display: flex;
  align-items: center;
}

.testimonials .testimonial-item .testimonial-footer .testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 15px;
  border: 3px solid color-mix(in srgb, var(--accent-color), transparent 80%);
}

.testimonials .testimonial-item .testimonial-footer .testimonial-author div h5 {
  margin: 0 0 5px;
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color);
}

.testimonials .testimonial-item .testimonial-footer .testimonial-author div span {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
}

.testimonials .testimonial-item .testimonial-footer .quote-icon {
  font-size: 36px;
  color: color-mix(in srgb, var(--accent-color), transparent 70%);
  line-height: 1;
}

.testimonials .testimonial-item .testimonial-footer .quote-icon i {
  transform: scaleX(-1);
}

@media (max-width: 768px) {
  .testimonials .testimonial-item {
    padding: 25px 20px;
  }

  .testimonials .testimonial-item p {
    font-size: 15px;
    margin-bottom: 20px;
  }

  .testimonials .testimonial-item .testimonial-footer .testimonial-author img {
    width: 45px;
    height: 45px;
  }

  .testimonials .testimonial-item .testimonial-footer .testimonial-author div h5 {
    font-size: 16px;
  }

  .testimonials .testimonial-item .testimonial-footer .testimonial-author div span {
    font-size: 13px;
  }

  .testimonials .testimonial-item .testimonial-footer .quote-icon {
    font-size: 30px;
  }
}

@media (max-width: 576px) {
  .testimonials .testimonial-item {
    padding: 20px 15px;
  }

  .testimonials .testimonial-item .testimonial-footer .testimonial-author img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }
}

/*--------------------------------------------------------------
# How We Work Section
--------------------------------------------------------------*/
.how-we-work .steps-wrapper {
  position: relative;
  padding: 20px 0;
}

.how-we-work .steps-wrapper::before {
  content: "";
  position: absolute;
  width: 2px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 85%);
  top: 0;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.how-we-work .step-item {
  margin-bottom: 50px;
  width: 100%;
  position: relative;
}

.how-we-work .step-item:last-child {
  margin-bottom: 0;
}

.how-we-work .step-item:nth-child(even) .step-content {
  flex-direction: row-reverse;
}

.how-we-work .step-content {
  display: flex;
  align-items: center;
  gap: 30px;
}

.how-we-work .step-icon {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  background-color: var(--surface-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border: 2px solid color-mix(in srgb, var(--accent-color), transparent 85%);
  transition: all 0.3s ease-in-out;
}

.how-we-work .step-icon i {
  font-size: 32px;
  color: var(--accent-color);
  transition: transform 0.3s ease-in-out;
}

.how-we-work .step-info {
  flex: 1;
  background-color: var(--surface-color);
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease-in-out;
}

.how-we-work .step-info:hover {
  transform: translateY(-5px);
}

.how-we-work .step-number {
  display: inline-block;
  font-family: var(--heading-font);
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--accent-color);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}

.how-we-work h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  font-weight: 600;
  color: var(--heading-color);
}

.how-we-work p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 0;
}

@media (max-width: 991px) {
  .how-we-work .steps-wrapper::before {
    left: 25px;
  }

  .how-we-work .step-item .step-content {
    flex-direction: row !important;
  }

  .how-we-work .step-icon {
    width: 60px;
    height: 60px;
  }

  .how-we-work .step-icon i {
    font-size: 24px;
  }

  .how-we-work .step-info {
    padding: 20px;
  }

  .how-we-work h3 {
    font-size: 1.3rem;
  }
}

@media (max-width: 767px) {
  .how-we-work .step-content {
    gap: 20px;
  }

  .how-we-work .step-icon {
    width: 50px;
    height: 50px;
  }

  .how-we-work .step-icon i {
    font-size: 20px;
  }

  .how-we-work .step-info {
    padding: 15px;
  }

  .how-we-work h3 {
    font-size: 1.2rem;
  }

  .how-we-work p {
    font-size: 0.95rem;
  }
}

/*--------------------------------------------------------------
# Faq Section
--------------------------------------------------------------*/
.faq .faq-tabs .nav-pills {
  display: inline-flex;
  padding: 8px;
  background-color: color-mix(in srgb, var(--default-color), transparent 95%);
  border-radius: 50px;
}

.faq .faq-tabs .nav-pills .nav-item {
  margin: 0 5px;
}

.faq .faq-tabs .nav-pills .nav-item:first-child {
  margin-left: 0;
}

.faq .faq-tabs .nav-pills .nav-item:last-child {
  margin-right: 0;
}

.faq .faq-tabs .nav-pills .nav-link {
  padding: 10px 20px;
  border-radius: 50px;
  font-weight: 500;
  color: var(--default-color);
  transition: all 0.3s ease;
}

.faq .faq-tabs .nav-pills .nav-link:hover {
  color: var(--accent-color);
}

.faq .faq-tabs .nav-pills .nav-link.active {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.faq .faq-tabs .nav-pills .nav-link i {
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .faq .faq-tabs .nav-pills {
    flex-wrap: wrap;
    justify-content: center;
  }

  .faq .faq-tabs .nav-pills .nav-item {
    margin: 5px;
  }
}

.faq .faq-list .faq-item {
  margin-bottom: 20px;
  border-radius: 10px;
  background-color: var(--surface-color);
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq .faq-list .faq-item:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.faq .faq-list .faq-item h3 {
  display: flex;
  align-items: center;
  padding: 20px 25px;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  background-color: var(--surface-color);
  transition: all 0.3s ease;
  position: relative;
}

.faq .faq-list .faq-item h3:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
}

.faq .faq-list .faq-item h3 .num {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-right: 15px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 85%);
  color: var(--accent-color);
  border-radius: 50%;
  font-size: 0.9rem;
  font-weight: 700;
  flex-shrink: 0;
}

.faq .faq-list .faq-item h3 .question {
  flex: 1;
}

.faq .faq-list .faq-item h3 .faq-toggle {
  font-size: 1.2rem;
  transition: all 0.3s ease;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-left: 15px;
}

.faq .faq-list .faq-item .faq-content {
  padding: 15px;
  display: none;
}

.faq .faq-list .faq-item .faq-content p {
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.faq .faq-list .faq-item .faq-content p:last-child {
  margin-bottom: 0;
  overflow: hidden;
}

.faq .faq-list .faq-item.faq-active h3 {
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
}

.faq .faq-list .faq-item.faq-active h3 .faq-toggle {
  transform: rotate(45deg);
  color: var(--accent-color);
}

.faq .faq-list .faq-item.faq-active .faq-content {
  display: block;
}

.faq .faq-cta {
  background-color: color-mix(in srgb, var(--accent-color), transparent 92%);
  padding: 30px;
  border-radius: 10px;
}

.faq .faq-cta p {
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.faq .faq-cta .btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  padding: 10px 25px;
  border-radius: 50px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.faq .faq-cta .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), #000 15%);
  border-color: color-mix(in srgb, var(--accent-color), #000 15%);
  transform: translateY(-2px);
}

@media (max-width: 576px) {
  .faq .faq-list .faq-item h3 {
    padding: 15px 20px;
    font-size: 1rem;
  }

  .faq .faq-list .faq-item h3 .num {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    font-size: 0.8rem;
  }

  .faq .faq-list .faq-item .faq-content .content-inner {
    padding: 0 20px;
  }

  .faq .faq-list .faq-item .faq-content.faq-active .content-inner {
    padding: 15px 20px;
  }
}

/*--------------------------------------------------------------
# Call To Action Section
--------------------------------------------------------------*/
.call-to-action .cta-wrapper {
  display: flex;
  align-items: center;
  gap: 3rem;
  background-color: var(--surface-color);
  border-radius: 1rem;
  padding: 3rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.call-to-action .cta-content {
  position: relative;
  z-index: 2;
  flex: 1;
}

.call-to-action .cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.call-to-action .cta-content p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  max-width: 600px;
  opacity: 0.9;
}

.call-to-action .cta-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.call-to-action .cta-buttons .btn-primary {
  padding: 0.8rem 2rem;
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.call-to-action .cta-buttons .btn-primary:hover {
  background-color: color-mix(in srgb, var(--accent-color), white 15%);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.call-to-action .cta-buttons .btn-outline {
  padding: 0.8rem 2rem;
  background-color: transparent;
  color: var(--default-color);
  border: 2px solid var(--default-color);
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.call-to-action .cta-buttons .btn-outline:hover {
  background-color: var(--contrast-color);
  color: var(--heading-color);
  transform: translateY(-3px);
}

.call-to-action .cta-image {
  position: relative;
  z-index: 2;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.call-to-action .cta-image img {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.call-to-action .cta-image img:hover {
  transform: translateY(-5px);
}

@media (max-width: 992px) {
  .call-to-action .cta-wrapper {
    flex-direction: column;
    padding: 2.5rem;
  }

  .call-to-action .cta-content {
    text-align: center;
  }

  .call-to-action .cta-content h2 {
    font-size: 2rem;
  }

  .call-to-action .cta-content p {
    margin-left: auto;
    margin-right: auto;
  }

  .call-to-action .cta-buttons {
    justify-content: center;
  }

  .call-to-action .cta-image img {
    max-width: 80%;
  }
}

@media (max-width: 768px) {
  .call-to-action .cta-wrapper {
    padding: 2rem;
  }

  .call-to-action .cta-content h2 {
    font-size: 1.8rem;
  }

  .call-to-action .cta-content p {
    font-size: 1rem;
  }

  .call-to-action .cta-buttons .btn-primary,
  .call-to-action .cta-buttons .btn-outline {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/*--------------------------------------------------------------
# Team Section
--------------------------------------------------------------*/
.team .row {
  align-items: center;
}

@media (max-width: 991px) {
  .team .team-intro {
    margin-bottom: 40px;
  }
}

.team .team-intro .team-intro-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.team .team-intro .team-intro-content h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 15px;
}

.team .team-intro .team-intro-content h2:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--accent-color);
}

.team .team-intro .team-intro-content p {
  margin-bottom: 30px;
  font-size: 16px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.team .team-intro .team-navigation {
  display: flex;
  gap: 10px;
}

.team .team-intro .team-navigation button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid var(--accent-color);
  color: var(--accent-color);
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.team .team-intro .team-navigation button:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
}

.team .member-card {
  display: flex;
  flex-direction: column;
  background-color: var(--surface-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team .member-card:hover {
  transform: translateY(-10px);
}

.team .member-card:hover .member-image:before {
  opacity: 0.7;
}

.team .member-card .member-image {
  position: relative;
  overflow: hidden;
  height: 240px;
}

.team .member-card .member-image:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
  opacity: 0.4;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.team .member-card .member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.team .member-card .member-image:hover img {
  transform: scale(1.05);
}

.team .member-card .member-info {
  padding: 25px;
  position: relative;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.team .member-card .member-info h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 5px;
}

.team .member-card .member-info span {
  color: var(--accent-color);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 15px;
}

.team .member-card .member-social {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.team .member-card .member-social a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  border-radius: 6px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.team .member-card .member-social a:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-3px);
}

.team .member-card .member-bio p {
  font-size: 14px;
  line-height: 1.6;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 0;
}

.team .team-carousel-wrap {
  overflow-x: hidden;
  padding: 30px 20px;
}

.team .team-carousel {
  overflow: visible;
}

.team .team-carousel .swiper-slide {
  height: auto;
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact {
  background-color: color-mix(in srgb, var(--background-color), #f5f8fd 30%);
}

.contact .container {
  max-width: 1200px;
}

.contact .contact-main-wrapper {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}

@media (min-width: 992px) {
  .contact .contact-main-wrapper {
    grid-template-columns: 45% 55%;
    min-height: 600px;
  }
}

.contact .map-wrapper {
  height: 300px;
  width: 100%;
  border-radius: 16px;
  overflow: hidden;
}

@media (min-width: 992px) {
  .contact .map-wrapper {
    height: 100%;
    position: sticky;
    top: 100px;
  }
}

.contact .contact-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.contact .contact-cards-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

@media (min-width: 576px) {
  .contact .contact-cards-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

.contact .contact-card {
  background-color: var(--surface-color);
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: flex-start;
  gap: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.04);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact .contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.contact .contact-card .icon-box {
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  background-color: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.contact .contact-card .icon-box i {
  font-size: 22px;
  color: var(--accent-color);
}

.contact .contact-card .contact-text h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--heading-color);
}

.contact .contact-card .contact-text p {
  font-size: 14px;
  line-height: 1.5;
  color: var(--default-color);
  margin-bottom: 0;
}

.contact .contact-form-container {
  background-color: var(--surface-color);
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.contact .contact-form-container h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--heading-color);
  position: relative;
  padding-left: 15px;
}

.contact .contact-form-container h3:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: var(--accent-color);
  border-radius: 2px;
}

.contact .contact-form-container>p {
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 25px;
  color: var(--default-color);
}

.contact .contact-form-container .php-email-form .form-control {
  height: auto;
  padding: 14px 20px;
  border-radius: 10px;
  background-color: color-mix(in srgb, var(--background-color), #f5f8fd 30%);
  border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
  color: var(--default-color);
  transition: all 0.3s ease;
}

.contact .contact-form-container .php-email-form .form-control:focus {
  background-color: var(--surface-color);
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--accent-color), transparent 85%);
}

.contact .contact-form-container .php-email-form .form-control::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 60%);
}

.contact .contact-form-container .php-email-form textarea.form-control {
  min-height: 140px;
}

.contact .contact-form-container .php-email-form .form-submit {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
}

@media (max-width: 576px) {
  .contact .contact-form-container .php-email-form .form-submit {
    flex-direction: column;
    align-items: flex-start;
  }
}

.contact .contact-form-container .php-email-form button {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  border: none;
  padding: 12px 28px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 15px;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.contact .contact-form-container .php-email-form button:hover {
  background-color: color-mix(in srgb, var(--accent-color), #000 15%);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px color-mix(in srgb, var(--accent-color), transparent 75%);
}

.contact .contact-form-container .php-email-form .social-links {
  display: flex;
  gap: 12px;
}

.contact .contact-form-container .php-email-form .social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: color-mix(in srgb, var(--background-color), #f5f8fd 20%);
  color: var(--heading-color);
  font-size: 16px;
  transition: all 0.3s ease;
}

.contact .contact-form-container .php-email-form .social-links a:hover {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  transform: translateY(-3px);
}

@media (max-width: 768px) {
  .contact .contact-form-container {
    padding: 25px 20px;
  }

  .contact .contact-form-container h3 {
    font-size: 22px;
  }
}

/*--------------------------------------------------------------
# Service Details Section
--------------------------------------------------------------*/
.service-details .swiper-wrapper {
  height: auto !important;
}

.service-details .service-sidebar {
  position: sticky;
  top: 100px;
}

.service-details .service-sidebar .service-overview {
  background: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
}

.service-details .service-sidebar .service-overview .overview-header {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.service-details .service-sidebar .service-overview .overview-header i {
  font-size: 24px;
}

.service-details .service-sidebar .service-overview .overview-header h3 {
  color: var(--contrast-color);
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.service-details .service-sidebar .service-overview .overview-content {
  padding: 30px;
}

.service-details .service-sidebar .service-overview .overview-content h2 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--heading-color);
}

.service-details .service-sidebar .service-overview .overview-content p {
  margin-bottom: 25px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.service-details .service-sidebar .service-overview .overview-content .cta-button .btn-get-started {
  display: inline-block;
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 12px 25px;
  border-radius: 8px;
  font-weight: 600;
  transition: 0.3s;
  text-align: center;
  width: 100%;
}

.service-details .service-sidebar .service-overview .overview-content .cta-button .btn-get-started:hover {
  background: color-mix(in srgb, var(--accent-color), black 10%);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.service-details .service-sidebar .key-benefits {
  background: var(--surface-color);
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
}

.service-details .service-sidebar .key-benefits h4 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 15px;
}

.service-details .service-sidebar .key-benefits h4:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--accent-color);
}

.service-details .service-sidebar .key-benefits ul {
  padding-left: 0;
  list-style: none;
}

.service-details .service-sidebar .key-benefits ul li {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  color: color-mix(in srgb, var(--default-color), transparent 15%);
}

.service-details .service-sidebar .key-benefits ul li i {
  color: var(--accent-color);
  font-size: 18px;
  margin-right: 12px;
}

.service-details .service-sidebar .contact-card {
  background: var(--surface-color);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
}

.service-details .service-sidebar .contact-card .contact-header {
  background: var(--accent-color);
  color: var(--contrast-color);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.service-details .service-sidebar .contact-card .contact-header i {
  font-size: 24px;
}

.service-details .service-sidebar .contact-card .contact-header h4 {
  color: var(--contrast-color);
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.service-details .service-sidebar .contact-card .contact-info {
  padding: 30px;
}

.service-details .service-sidebar .contact-card .contact-info .info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.service-details .service-sidebar .contact-card .contact-info .info-row:last-child {
  margin-bottom: 0;
}

.service-details .service-sidebar .contact-card .contact-info .info-row i {
  font-size: 20px;
  color: var(--accent-color);
  margin-right: 15px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  border-radius: 50%;
}

.service-details .service-sidebar .contact-card .contact-info .info-row div span {
  display: block;
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  margin-bottom: 5px;
}

.service-details .service-sidebar .contact-card .contact-info .info-row div p {
  margin: 0;
  font-weight: 600;
  color: var(--heading-color);
}

.service-details .service-content .image-gallery {
  margin-bottom: 40px;
}

.service-details .service-content .image-gallery .service-details-slider {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.service-details .service-content .image-gallery .service-details-slider .swiper-pagination {
  bottom: 20px;
}

.service-details .service-content .image-gallery .service-details-slider .swiper-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background: var(--contrast-color);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.service-details .service-content .image-gallery .service-details-slider .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
  width: 25px;
  border-radius: 5px;
}

.service-details .service-content .image-gallery .service-details-slider .swiper-button-next,
.service-details .service-content .image-gallery .service-details-slider .swiper-button-prev {
  width: 45px;
  height: 45px;
  background: var(--accent-color);
  border-radius: 50%;
  color: var(--contrast-color);
  transition: 0.3s;
}

.service-details .service-content .image-gallery .service-details-slider .swiper-button-next::after,
.service-details .service-content .image-gallery .service-details-slider .swiper-button-prev::after {
  font-size: 16px;
  font-weight: bold;
}

.service-details .service-content .image-gallery .service-details-slider .swiper-button-next:hover,
.service-details .service-content .image-gallery .service-details-slider .swiper-button-prev:hover {
  background: color-mix(in srgb, var(--accent-color), black 15%);
  transform: scale(1.1);
}

.service-details .service-content .section-header {
  margin-bottom: 25px;
}

.service-details .service-content .section-header h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
}

.service-details .service-content .section-header .divider {
  width: 70px;
  height: 3px;
  background: var(--accent-color);
}

.service-details .service-content .details-content {
  margin-bottom: 40px;
}

.service-details .service-content .details-content p {
  margin-bottom: 15px;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  line-height: 1.7;
}

.service-details .service-content .details-content p:last-child {
  margin-bottom: 0;
}

.service-details .service-content .service-features {
  margin-bottom: 40px;
}

.service-details .service-content .service-features .feature-card {
  background: var(--surface-color);
  border-radius: 12px;
  padding: 30px;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: 0.3s;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.service-details .service-content .service-features .feature-card:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0;
  background: color-mix(in srgb, var(--accent-color), transparent 95%);
  z-index: -1;
  transition: 0.4s;
}

.service-details .service-content .service-features .feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-details .service-content .service-features .feature-card:hover:before {
  height: 100%;
}

.service-details .service-content .service-features .feature-card:hover .icon-wrapper {
  background: var(--accent-color);
  color: var(--contrast-color);
  transform: scale(1.1);
}

.service-details .service-content .service-features .feature-card .icon-wrapper {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: color-mix(in srgb, var(--accent-color), transparent 90%);
  color: var(--accent-color);
  font-size: 26px;
  border-radius: 50%;
  margin-bottom: 20px;
  transition: 0.3s;
}

.service-details .service-content .service-features .feature-card h4 {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 15px;
}

.service-details .service-content .service-features .feature-card p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 0;
}

.service-details .service-content .implementation-steps .step-container {
  position: relative;
}

.service-details .service-content .implementation-steps .step-container:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 18px;
  width: 2px;
  background: color-mix(in srgb, var(--accent-color), transparent 80%);
}

.service-details .service-content .implementation-steps .step-container .step {
  display: flex;
  margin-bottom: 30px;
  position: relative;
}

.service-details .service-content .implementation-steps .step-container .step:last-child {
  margin-bottom: 0;
}

.service-details .service-content .implementation-steps .step-container .step .step-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-color);
  color: var(--contrast-color);
  font-weight: 700;
  border-radius: 50%;
  margin-right: 20px;
  z-index: 2;
  font-size: 14px;
}

.service-details .service-content .implementation-steps .step-container .step .step-content {
  flex: 1;
}

.service-details .service-content .implementation-steps .step-container .step .step-content h4 {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 10px;
}

.service-details .service-content .implementation-steps .step-container .step .step-content p {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  margin-bottom: 0;
}

@media (max-width: 991px) {
  .service-details .service-sidebar {
    position: relative;
    top: 0;
    margin-bottom: 40px;
  }
}

@media (max-width: 767px) {
  .service-details .service-content .service-features .feature-card {
    margin-bottom: 20px;
  }
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/
.starter-section {
  /* Add your styles here */
}