/* 本地字体定义，替代Google Fonts CDN */

/* Roboto 字体 */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 100;
  src: local('Roboto Thin'), local('Roboto-Thin'),
       url('./roboto/roboto-v27-chinese-simplified_latin-100.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-100.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: italic;
  font-weight: 100;
  src: local('Roboto Thin Italic'), local('Roboto-ThinItalic'),
       url('./roboto/roboto-v27-chinese-simplified_latin-100italic.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-100italic.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  src: local('Roboto Light'), local('Roboto-Light'),
       url('./roboto/roboto-v27-chinese-simplified_latin-300.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-300.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: italic;
  font-weight: 300;
  src: local('Roboto Light Italic'), local('Roboto-LightItalic'),
       url('./roboto/roboto-v27-chinese-simplified_latin-300italic.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-300italic.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  src: local('Roboto'), local('Roboto-Regular'),
       url('./roboto/roboto-v27-chinese-simplified_latin-regular.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-regular.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: italic;
  font-weight: 400;
  src: local('Roboto Italic'), local('Roboto-Italic'),
       url('./roboto/roboto-v27-chinese-simplified_latin-italic.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-italic.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  src: local('Roboto Medium'), local('Roboto-Medium'),
       url('./roboto/roboto-v27-chinese-simplified_latin-500.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-500.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: italic;
  font-weight: 500;
  src: local('Roboto Medium Italic'), local('Roboto-MediumItalic'),
       url('./roboto/roboto-v27-chinese-simplified_latin-500italic.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-500italic.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  src: local('Roboto Bold'), local('Roboto-Bold'),
       url('./roboto/roboto-v27-chinese-simplified_latin-700.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-700.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: italic;
  font-weight: 700;
  src: local('Roboto Bold Italic'), local('Roboto-BoldItalic'),
       url('./roboto/roboto-v27-chinese-simplified_latin-700italic.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-700italic.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 900;
  src: local('Roboto Black'), local('Roboto-Black'),
       url('./roboto/roboto-v27-chinese-simplified_latin-900.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-900.woff') format('woff');
}

@font-face {
  font-family: 'Roboto';
  font-style: italic;
  font-weight: 900;
  src: local('Roboto Black Italic'), local('Roboto-BlackItalic'),
       url('./roboto/roboto-v27-chinese-simplified_latin-900italic.woff2') format('woff2'),
       url('./roboto/roboto-v27-chinese-simplified_latin-900italic.woff') format('woff');
}

/* Montserrat 字体 */
@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: local('Montserrat Regular'), local('Montserrat-Regular'),
       url('./montserrat/montserrat-v15-latin-regular.woff2') format('woff2'),
       url('./montserrat/montserrat-v15-latin-regular.woff') format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src: local('Montserrat Medium'), local('Montserrat-Medium'),
       url('./montserrat/montserrat-v15-latin-500.woff2') format('woff2'),
       url('./montserrat/montserrat-v15-latin-500.woff') format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 700;
  src: local('Montserrat Bold'), local('Montserrat-Bold'),
       url('./montserrat/montserrat-v15-latin-700.woff2') format('woff2'),
       url('./montserrat/montserrat-v15-latin-700.woff') format('woff');
}

/* Raleway 字体 */
@font-face {
  font-family: 'Raleway';
  font-style: normal;
  font-weight: 400;
  src: local('Raleway'), local('Raleway-Regular'),
       url('./raleway/raleway-v17-latin-regular.woff2') format('woff2'),
       url('./raleway/raleway-v17-latin-regular.woff') format('woff');
}

@font-face {
  font-family: 'Raleway';
  font-style: normal;
  font-weight: 500;
  src: local('Raleway Medium'), local('Raleway-Medium'),
       url('./raleway/raleway-v17-latin-500.woff2') format('woff2'),
       url('./raleway/raleway-v17-latin-500.woff') format('woff');
}

@font-face {
  font-family: 'Raleway';
  font-style: normal;
  font-weight: 700;
  src: local('Raleway Bold'), local('Raleway-Bold'),
       url('./raleway/raleway-v17-latin-700.woff2') format('woff2'),
       url('./raleway/raleway-v17-latin-700.woff') format('woff');
}