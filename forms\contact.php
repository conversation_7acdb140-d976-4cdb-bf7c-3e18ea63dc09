<?php
  /**
  * Requires the "PHP Email Form" library
  * The "PHP Email Form" library is available only in the pro version of the template
  * The library should be uploaded to: vendor/php-email-form/php-email-form.php
  * For more info and help: https://bootstrapmade.com/php-email-form/
  */

  // 保存留言到contact.md文件
  function saveToContactMd($name, $email, $subject, $message) {
    // 构建留言内容（Markdown格式）
    $timestamp = date('Y-m-d H:i:s');
    $content = "\n\n## 留言 - {$timestamp}\n\n";
    $content .= "- **姓名**: {$name}\n";
    $content .= "- **邮箱**: {$email}\n";
    $content .= "- **主题**: {$subject}\n\n";
    $content .= "### 留言内容:\n\n{$message}\n\n---\n";
    
    // 打开contact.md文件（追加模式）
    $file = fopen("../contact.md", "a");
    if ($file) {
      // 写入留言内容
      fwrite($file, $content);
      fclose($file);
      return true;
    } else {
      return false;
    }
  }

  // 处理表单提交
  if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = isset($_POST['name']) ? $_POST['name'] : '';
    $email = isset($_POST['email']) ? $_POST['email'] : '';
    $subject = isset($_POST['subject']) ? $_POST['subject'] : '';
    $message = isset($_POST['message']) ? $_POST['message'] : '';
    
    // 保存留言到contact.md文件
    $saved = saveToContactMd($name, $email, $subject, $message);
    
    // 如果需要同时发送邮件，可以保留以下代码
    // Replace <EMAIL> with your real receiving email address
    $receiving_email_address = '<EMAIL>';

    if (file_exists($php_email_form = '../assets/vendor/php-email-form/php-email-form.php')) {
      include($php_email_form);
      
      $contact = new PHP_Email_Form;
      $contact->ajax = true;
      
      $contact->to = $receiving_email_address;
      $contact->from_name = $name;
      $contact->from_email = $email;
      $contact->subject = $subject;

      // Uncomment below code if you want to use SMTP to send emails. You need to enter your correct SMTP credentials
      /*
      $contact->smtp = array(
        'host' => 'example.com',
        'username' => 'example',
        'password' => 'pass',
        'port' => '587'
      );
      */

      $contact->add_message($name, 'From');
      $contact->add_message($email, 'Email');
      if(isset($_POST['phone'])) {
        $contact->add_message($_POST['phone'], 'Phone');
      }
      $contact->add_message($message, 'Message', 10);

      // 发送邮件
      $contact->send();
    }
    
    // 返回成功信息
    if ($saved) {
      echo "OK";
    } else {
      echo "无法保存留言到contact.md文件";
    }
  } else {
    echo "无效的请求方法";
  }
?>
