# 山东小三线工厂图片说明

## 已添加图片
- factory1.jpg 到 factory8.jpg：已存在，用于展示前8个工厂
- xiaosanxian1.jpg 到 xiaosanxian6.jpg：已存在，用于展示小三线历史

## 需要添加的图片
- factory9.jpg：山东光明机器厂（代号：9347）的图片
- factory10.jpg：山东民丰机械厂（代号：9381）的图片
- factory11.jpg：山东新华翻砂厂（代号：9363）的图片
- factory12.jpg：山东裕华修配厂（代号：9354）的图片
- factory13.jpg：山东泰山光学仪器厂（代号：5808）的图片

## 图片要求
1. 图片尺寸建议保持一致，与现有factory1-8.jpg相同
2. 图片格式为JPG
3. 图片内容应与对应工厂相关，可以是工厂厂区、生产场景、产品或历史照片

请将这些图片添加到assets/img/about目录中，以确保网页能正确显示所有工厂信息。

## 临时解决方案
在正式图片准备好之前，可以临时复制factory8.jpg并重命名为factory9.jpg到factory13.jpg，作为占位图片使用。

```
# 复制命令参考（PowerShell）
Copy-Item -Path "c:\Git\Scout\assets\img\about\factory8.jpg" -Destination "c:\Git\Scout\assets\img\about\factory9.jpg"
Copy-Item -Path "c:\Git\Scout\assets\img\about\factory8.jpg" -Destination "c:\Git\Scout\assets\img\about\factory10.jpg"
Copy-Item -Path "c:\Git\Scout\assets\img\about\factory8.jpg" -Destination "c:\Git\Scout\assets\img\about\factory11.jpg"
Copy-Item -Path "c:\Git\Scout\assets\img\about\factory8.jpg" -Destination "c:\Git\Scout\assets\img\about\factory12.jpg"
Copy-Item -Path "c:\Git\Scout\assets\img\about\factory8.jpg" -Destination "c:\Git\Scout\assets\img\about\factory13.jpg"
```