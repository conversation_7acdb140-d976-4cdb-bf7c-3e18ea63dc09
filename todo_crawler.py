import re
import os
import datetime
from urllib.parse import urljoin
import requests
from bs4 import BeautifulSoup

def extract_links_from_todo():
    """从 todo.md 文件中提取链接"""
    print("开始从 todo.md 提取链接...")
    
    # 读取 todo.md 文件
    try:
        with open("todo.md", "r", encoding="utf-8") as file:
            content = file.read()
        print(f"成功读取 todo.md 文件，大小: {len(content)} 字节")
    except Exception as e:
        print(f"读取 todo.md 文件时出错: {str(e)}")
        return None
    
    # 使用正则表达式提取链接
    # 匹配格式: - [ ] 标题: URL
    link_pattern = r"- \[ \] (.+?):\s+(https?://[^\s]+)"
    matches = re.findall(link_pattern, content)
    
    if not matches:
        print("未在 todo.md 中找到链接")
        return None
    
    print(f"从 todo.md 中提取到 {len(matches)} 个链接")
    return matches

def crawl_links(links):
    """爬取链接内容"""
    results = []
    
    for i, (title, url) in enumerate(links):
        print(f"[{i+1}/{len(links)}] 正在爬取: {title} - {url}")
        
        try:
            # 发送请求获取页面内容，设置超时为 10 秒
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                # 使用 BeautifulSoup 解析页面内容
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取页面标题
                page_title = soup.title.text if soup.title else "无标题"
                
                # 提取页面正文内容（简化处理，仅获取前 200 个字符）
                content = soup.get_text(" ", strip=True)
                summary = content[:200] + "..." if len(content) > 200 else content
                
                results.append({
                    "title": title,
                    "url": url,
                    "page_title": page_title,
                    "summary": summary
                })
                
                print(f"  ✓ 成功爬取 {title}")
            else:
                print(f"  ✗ 无法访问 {url}，状态码: {response.status_code}")
                results.append({
                    "title": title,
                    "url": url,
                    "error": f"状态码: {response.status_code}"
                })
        except Exception as e:
            print(f"  ✗ 爬取 {url} 时出错: {str(e)}")
            results.append({
                "title": title,
                "url": url,
                "error": str(e)
            })
        
        # 简单的延时，避免请求过于频繁
        # time.sleep(1)
    
    return results

def save_results(results, offline_mode=True):
    """保存爬取结果"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_file = f"crawl_results_{timestamp}.md"
    
    with open(output_file, "w", encoding="utf-8") as file:
        file.write(f"# todo.md 链接爬取结果 ({timestamp})\n\n")
        
        if offline_mode:
            file.write("## 链接列表（离线模式）\n\n")
            for i, (title, url) in enumerate(results):
                file.write(f"{i+1}. [{title}]({url})\n")
        else:
            file.write("## 爬取结果\n\n")
            for i, result in enumerate(results):
                file.write(f"### {i+1}. {result['title']}\n\n")
                file.write(f"- URL: {result['url']}\n")
                
                if 'error' in result:
                    file.write(f"- 状态: ❌ 爬取失败\n")
                    file.write(f"- 错误: {result['error']}\n")
                else:
                    file.write(f"- 状态: ✅ 爬取成功\n")
                    file.write(f"- 页面标题: {result['page_title']}\n")
                    file.write(f"- 内容摘要: {result['summary']}\n")
                
                file.write("\n---\n\n")
    
    print(f"\n结果已保存到: {output_file}")
    return output_file

def main():
    print("===== todo.md 链接爬虫程序 =====\n")
    
    # 提取链接
    links = extract_links_from_todo()
    if not links:
        return
    
    # 询问是否进行在线爬取
    print("\n选择操作模式:")
    print("1. 离线模式 - 仅提取链接并保存")
    print("2. 在线模式 - 爬取每个链接的内容（可能需要较长时间）")
    
    try:
        choice = input("请输入选择 (1/2)，默认为离线模式: ").strip() or "1"
        
        if choice == "2":
            # 在线爬取
            print("\n开始在线爬取链接内容...")
            results = crawl_links(links)
            save_results(results, offline_mode=False)
        else:
            # 离线模式，仅保存链接
            print("\n使用离线模式，仅保存链接...")
            save_results(links, offline_mode=True)
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
    
    print("\n程序执行完毕")

# 如果直接运行此脚本，则执行 main 函数
if __name__ == "__main__":
    main()