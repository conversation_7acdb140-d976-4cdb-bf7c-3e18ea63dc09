# Google Fonts 本地化使用说明

## 概述

本目录包含了网站所需的Google Fonts字体文件的本地化版本，用于替代从Google CDN加载字体，以提高在中国大陆的访问速度和稳定性。

## 字体下载方法

请按照以下步骤下载所需的字体文件：

1. 访问 [Google Webfonts Helper](https://gwfh.mranftl.com/) 网站
2. 搜索并选择以下字体：
   - Roboto (各种粗细和样式)
   - Montserrat (各种粗细和样式)
   - Raleway (各种粗细和样式)
3. 在右侧面板中：
   - 选择「字符集」为「拉丁文扩展」和「中文简体」
   - 选择「样式」为「现代浏览器」(仅包含WOFF和WOFF2格式)
4. 复制提供的CSS代码
5. 下载字体文件压缩包
6. 将下载的字体文件解压到以下对应目录：
   - Roboto字体文件 → `assets/fonts/google-fonts/roboto/`
   - Montserrat字体文件 → `assets/fonts/google-fonts/montserrat/`
   - Raleway字体文件 → `assets/fonts/google-fonts/raleway/`

## 目录结构

```
assets/fonts/google-fonts/
├── fonts.css           # 字体定义CSS文件
├── roboto/            # Roboto字体文件目录
├── montserrat/        # Montserrat字体文件目录
├── raleway/           # Raleway字体文件目录
└── README.md          # 本说明文件
```

## 使用方法

在HTML文件的`<head>`部分，将Google Fonts的CDN链接替换为本地字体CSS文件：

```html
<!-- 删除或注释掉以下Google Fonts CDN链接 -->
<!--
<link href="https://fonts.googleapis.com" rel="preconnect">
<link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;..." rel="stylesheet">
-->

<!-- 添加本地字体CSS文件链接 -->
<link href="assets/fonts/google-fonts/fonts.css" rel="stylesheet">
```

## 注意事项

1. 本地字体文件可能会增加项目大小，但会提高页面加载速度和稳定性
2. 确保字体文件的路径正确，否则可能导致字体无法正常显示
3. 如需添加新的字体或样式，请重复上述下载步骤并更新fonts.css文件